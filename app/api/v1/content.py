"""
内容总结API - LangGraph agent with direct LLM passthrough
"""
from fastapi import APIRouter
from fastapi.responses import J<PERSON><PERSON>esponse, StreamingResponse

from app.agents.base import agent_registry
from app.api.models import ContentSummaryRequest
from app.core.models import AgentType
from app.utils.logger import get_logger
import async<PERSON>
from typing import Any, Dict

logger = get_logger(__name__)
router = APIRouter()


@router.post("/summarize", 
             summary="Content Summarization",
             description="Summarize provided content (string or string[]) or chat history with smart context management; returns raw LLM API format.",)
async def summarize_content(request: ContentSummaryRequest):
    """Content summarization with LangGraph processing and raw LLM passthrough"""
    logger.info(f"Processing content summarization request")

    # Create content agent
    agent = agent_registry.create_agent(AgentType.CONTENT_UNDERSTANDING)
    # Branch: content is list -> multi-content summarize (map -> reduce)
    if isinstance(request.content, list):
        items = request.content
        MAX_ITEMS = 10
        if len(items) > MAX_ITEMS:
            logger.info(f"Multi-content summarize: truncating items from {len(items)} to {MAX_ITEMS}")
            items = items[:MAX_ITEMS]

        async def summarize_one(text: str) -> str:
            try:
                resp = await agent.process_summary(
                    content=text,
                    messages=None,
                    model=request.model,
                    stream=False,
                    max_tokens=400,
                    history_strategy="truncate",
                    concise_mode=True,
                )
                # resp is raw LLM result; try to extract plain text if dict-like
                if isinstance(resp, dict):
                    choices = resp.get("choices") or []
                    if choices:
                        msg = choices[0].get("message") or {}
                        content_txt = msg.get("content")
                        if isinstance(content_txt, str):
                            return content_txt
                if isinstance(resp, str):
                    return resp
            except Exception as e:
                logger.warning(f"Single item summarize failed: {e}")
            return ""

        # Map concurrently
        summaries = await asyncio.gather(*(summarize_one(t) for t in items), return_exceptions=False)
        # Build merged content with fixed segment markers
        header_note = "以下是多段内容的摘要集合：\n\n"
        merged_content = header_note + "\n\n".join(
            f"[片段{i+1}]\n{(s or '').strip()}" for i, s in enumerate(summaries)
        )

        # Reduce: call agent again on merged content (stream as requested)
        result = await agent.process_summary(
            content=merged_content,
            messages=None,
            model=request.model,
            stream=request.stream,
            max_tokens=1000,
            history_strategy="truncate",
            concise_mode=request.concise_mode,
        )

        if request.stream:
            async def stream_generator():
                async for line in result:
                    yield f"{line}\n\n"
            return StreamingResponse(
                stream_generator(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Headers": "*"
                }
            )
        else:
            return JSONResponse(status_code=200, content=result)

    # Default: single-content summarize (existing behavior)
    result = await agent.process_summary(
        content=request.content,
        messages=request.messages,
        model=request.model,
        stream=request.stream,
        max_tokens=None,
        history_strategy=request.history_strategy,
        concise_mode=request.concise_mode,
    )

    if request.stream:
        # Return raw LLM stream
        async def stream_generator():
            async for line in result:
                yield f"{line}\n\n"
        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )
    else:
        # Return raw LLM response
        return JSONResponse(status_code=200, content=result)

