from typing import List, Dict, Any, Tuple, Optional
import asyncio

from app.config import settings
from app.services.llm_service import llm_service
from .prompts import (
    build_history_summary_messages,
    build_high_compression_summary_messages,
    build_merge_chunk_summaries_messages,
)
from app.utils.logger import get_logger

logger = get_logger(__name__)


# 估算每条消息的固定额外token开销：
# - 包含role标记、消息边界/分隔符、格式化符号等
# - 不同模型/编码会有差异，此处取保守经验值8，便于预算略向上取整
# - 如需更精确可按模型微调，或在将来引入基于具体模型的消息编码估算
OVERHEAD_PER_MESSAGE = 8


def estimate_messages_tokens(messages: List[Dict[str, Any]]) -> int:
    total = 0
    for msg in messages:
        content = msg.get("content", "")
        if isinstance(content, str):
            total += settings.token_count(content)
        else:
            total += 64
        total += OVERHEAD_PER_MESSAGE
    return total


async def summarize_history(messages: List[Dict[str, Any]], model: Optional[str]) -> str:
    if not messages:
        return ""
    joined_history = "\n\n".join(
        f"[{m.get('role')}] {m.get('content','')}" for m in messages
    )
    try:
        req_messages = build_history_summary_messages(joined_history)
        resp = await llm_service.chat_completion(
            messages=req_messages, model=model, max_tokens=400, temperature=0.2
        )
        if isinstance(resp, dict):
            choices = resp.get("choices") or []
            if choices:
                first = choices[0]
                msg = first.get("message") or {}
                content = msg.get("content")
                if isinstance(content, str):
                    return content
        if isinstance(resp, str):
            return resp
    except Exception as e:
        logger.warning(
            f"History summarization failed, falling back to naive truncation: {e}"
        )
    joined = "\n\n".join(m.get("content", "") for m in messages)
    return joined[:2000]


async def fit_messages_into_budget(
    messages: List[Dict[str, Any]], budget_tokens: int, model: Optional[str]
) -> Tuple[List[Dict[str, Any]], int]:
    if not messages:
        return [], 0

    # Find the latest user message index
    last_user_idx = None
    for i in range(len(messages) - 1, -1, -1):
        if messages[i].get("role") == "user":
            last_user_idx = i
            break
    if last_user_idx is None:
        last_user_idx = len(messages) - 1

    tail_messages: List[Dict[str, Any]] = []
    # Include messages from the end until we hit the budget
    for i in range(len(messages) - 1, -1, -1):
        candidate = [messages[i]] + tail_messages
        tokens = estimate_messages_tokens(candidate)
        if tokens <= budget_tokens:
            tail_messages = candidate
        else:
            break

    # Ensure latest user message is present
    if all(m is not messages[last_user_idx] for m in tail_messages):
        forced_tail = [messages[last_user_idx]] + [
            m for m in tail_messages if m is not messages[last_user_idx]
        ]
        while estimate_messages_tokens(forced_tail) > budget_tokens and len(forced_tail) > 1:
            forced_tail.pop(1)
        tail_messages = forced_tail

    if len(tail_messages) == len(messages):
        return tail_messages, estimate_messages_tokens(tail_messages)

    head_count = len(messages) - len(tail_messages)
    head_messages = messages[:head_count]
    summary_text = await summarize_history(head_messages, model)
    summary_msg = {
        "role": "system",
        "content": f"以下为较早对话的简要摘要，用于提供上下文：\n{summary_text}",
    }

    final_messages = [summary_msg] + tail_messages
    total_tokens = estimate_messages_tokens(final_messages)

    if total_tokens > budget_tokens:
        excess = total_tokens - budget_tokens
        trim_chars = int(excess / 0.6) + 50
        truncated = summary_text[: max(0, len(summary_text) - trim_chars)]
        summary_msg["content"] = (
            f"以下为较早对话的简要摘要（已截断）：\n{truncated}"
        )
        final_messages = [summary_msg] + tail_messages
        total_tokens = estimate_messages_tokens(final_messages)

    return final_messages, total_tokens


def truncate_messages_into_budget(
    messages: List[Dict[str, Any]], budget_tokens: int
) -> Tuple[List[Dict[str, Any]], int]:
    """不通过大模型，仅按token预算从末尾截取最近的消息，确保尽量包含最近的用户消息。"""
    if not messages:
        return [], 0
    tail: List[Dict[str, Any]] = []
    for i in range(len(messages) - 1, -1, -1):
        candidate = [messages[i]] + tail
        tokens = estimate_messages_tokens(candidate)
        if tokens <= budget_tokens:
            tail = candidate
        else:
            break
    return tail, estimate_messages_tokens(tail)


def split_text_by_tokens(text: str, max_tokens: int) -> list[str]:
    """按大致token数量切块，尽量在段落/换行处分割。"""
    if not text:
        return []
    chunks: list[str] = []
    current: list[str] = []
    current_tokens = 0
    for para in text.split("\n\n"):
        t = settings.token_count(para)
        if current_tokens + t > max_tokens and current:
            chunks.append("\n\n".join(current))
            current = [para]
            current_tokens = t
        else:
            current.append(para)
            current_tokens += t
    if current:
        chunks.append("\n\n".join(current))
    return chunks



async def _summarize_chunk(chunk_text: str, model: Optional[str], concise_mode: bool = False) -> str:
    req = build_high_compression_summary_messages(chunk_text, concise_mode=concise_mode)
    resp = await llm_service.chat_completion(
        messages=req, model=model, max_tokens=400, temperature=0.2
    )
    if isinstance(resp, dict):
        cs = resp.get("choices") or []
        if cs:
            msg = cs[0].get("message") or {}
            c = msg.get("content")
            if isinstance(c, str):
                return c
    if isinstance(resp, str):
        return resp
    return ""


async def prepare_long_content_for_final(
    content: str, model: Optional[str], budget_tokens: int, concise_mode: bool = False
) -> str:
    """将超长内容转换为“前序摘要 + 最近片段全文”形式，供最终（可流式）调用。

    - 把内容按 per_chunk_budget 切块
    - 并发压缩前 N-1 块，合并为前序摘要
    - 拼接 "[前序摘要] + [最近片段全文]"，并在必要时裁剪摘要以适配预算
    """
    if not content:
        return ""
    reserve = settings.long_content_merge_reserve_tokens
    total_tokens = settings.token_count(content)
    n_chunks = max(1, (total_tokens + budget_tokens - 1) // budget_tokens)
    avg_target = max(
        settings.long_content_min_chunk_tokens, (total_tokens + n_chunks - 1) // n_chunks
    )
    per_chunk_budget = min(
        avg_target, max(settings.long_content_min_chunk_tokens, budget_tokens - reserve)
    )

    chunks = split_text_by_tokens(content, per_chunk_budget)
    if len(chunks) <= 1:
        return content

    last_chunk = chunks[-1]
    prev_chunks = chunks[:-1]

    # 并发压缩前 N-1 块
    tasks = [asyncio.create_task(_summarize_chunk(ch, model, concise_mode=concise_mode)) for ch in prev_chunks]
    prev_summaries = await asyncio.gather(*tasks, return_exceptions=True)
    # 展平异常为字符串
    summaries: List[str] = []
    for s in prev_summaries:
        if isinstance(s, Exception):
            logger.warning(f"Chunk summarize failed: {s}")
            summaries.append("")
        else:
            summaries.append(str(s))

    merged_input = "\n\n".join(f"[片段{i+1}]\n{t}" for i, t in enumerate(summaries))
    merge_req = build_merge_chunk_summaries_messages(merged_input, concise_mode=concise_mode)
    merged_resp = await llm_service.chat_completion(
        messages=merge_req, model=model, max_tokens=600, temperature=0.2
    )
    merged_text = ""
    if isinstance(merged_resp, dict):
        cs = merged_resp.get("choices") or []
        if cs:
            msg = cs[0].get("message") or {}
            c = msg.get("content")
            if isinstance(c, str):
                merged_text = c
    elif isinstance(merged_resp, str):
        merged_text = merged_resp

    # 组装最终输入：前序摘要 + 最近片段全文
    final_content = (
        f"[前序摘要]\n{merged_text}\n\n[最近片段全文]\n{last_chunk}"
    )

    # 如仍超预算，优先裁剪前序摘要，使 build_content_summary_messages 结果贴合预算
    from .prompts import build_content_summary_messages
    messages = build_content_summary_messages(final_content, concise_mode=concise_mode)
    if estimate_messages_tokens(messages) > budget_tokens:
        # 近似换算裁剪长度
        over = estimate_messages_tokens(messages) - budget_tokens
        trim_chars = int(over / 0.6) + 200
        merged_text = merged_text[:-max(0, trim_chars)] if trim_chars < len(merged_text) else ""
        final_content = f"[前序摘要]\n{merged_text}\n\n[最近片段全文]\n{last_chunk}"

    return final_content


